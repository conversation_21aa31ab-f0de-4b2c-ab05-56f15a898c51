<route lang="json5">
{
  style: {
    navigationBarTitleText: '条码成员信息变更',
  },
}
</route>

<script lang="ts" setup>
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ModifyStep from '@/components/agencyService/modifyStep.vue'
import ModifyTip from '@/components/agencyService/modifyTip.vue'
import { toAgencyPath } from '@/utils'
import { ServerType } from '@/enums'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { existUncompletedChange } = storeToRefs(userStore)

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit } =
  useCreateOrder({
    orderConfirmUrl: '/pages/orderConfirm/default',
    paymentSuccessUrl: '/pages/paymentSuccess/agencyService',
  })
</script>
<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <ModifyTip />
        <view class="o-line mt-4"></view>
        <ModifyStep />
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}办理说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view class="mt-4" id="targetElement">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view class="flex gap-1">
        <view
          class="py-3 flex-grow-1 flex items-center justify-center font-bold rounded-lg"
          :class="
            existUncompletedChange ? 'bg-white o-color-primary border' : 'o-bg-primary color-white'
          "
          @click="handleSubmit"
        >
          {{ existUncompletedChange ? '继续下单' : '提交方案' }}
        </view>
        <view
          v-if="existUncompletedChange"
          class="py-3 flex-grow-1 o-bg-primary color-white rounded-lg flex justify-center items-baseline"
          @click="toAgencyPath(ServerType.modifyService)"
        >
          <text class="text-sm">您有未完成的业务，</text>
          <text class="font-bold">前往办理</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
