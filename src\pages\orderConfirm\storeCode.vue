<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { useServiceStore } from '@/store/serviceStore'
import { storeToRefs } from 'pinia'
import {
  couponPageApi,
  getDouYinOrderPreDataApi,
  payOrderApi,
  priceTempListApi,
  sumbitOrderApi,
  SumbitOrderParams,
} from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { douYinRequestPayment, wxRequestPayment } from '@/components/mix'
import { couponTypeStr, Article, CouponType, ServerType } from '@/enums/index'
import DiscountBox from '@/components/Price/DiscountBox.vue'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { catchErrorAndNavigateBack } from '@/utils'
import { msgModalStore } from '@/store/msgModalStore'
import debounce from 'debounce'
const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const {
  couponId,
  orderCode,
  serverType,
  startBarCode,
  endBarCode,
  size,
  price,
  useCount,
  total,
  existCount,
  couponPrice,
  discount,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  couponType,
  isSelectedCoupon,
  vendorCode,
} = storeToRefs(useOrderStore)

const isShowStep = ref(true)
const btnDisable = ref(false)
const btnStr = ref('提交订单')
const agree = ref(false)
const isModalShow = ref(false)
const serveData = ref<any[]>([])
const theBest = reactive({
  nameNumber: '',
  price: '',
})

onLoad(() => {
  // console.log(option)
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()

  // 第一次获无优惠券的取订单价格
  getOrderPrice().then(() => {
    // 获取优惠券列表
    couponPageApi({
      isUsed: 0,
      isTimeOut: 0,
      serverType: ServerType.makeFilm, // 没有单独的店内码优惠券
      groupBy: '',
      needTotalCount: true,
      orderBy: '',
      orderDirection: OrderDirection.def,
      pageIndex: 1,
      pageSize: 10000,
      userId: userId.value,
    }).then((res) => {
      getOptimalCoupon(res.data)
      console.log('couponId.value', couponId.value)
      getOrderPrice()
    })
  })
})

onShow(() => {
  // onLoad 比 onShow 优先执行
  // 选择优惠券后，后退回到这逻辑
  if (isSelectedCoupon.value) {
    getOrderPrice()
    isSelectedCoupon.value = false
  }
})

// 没有单独的店内码优惠券，所有用ServerType.makeFilm
priceTempListApi({ tempType: ServerType.makeFilm }).then((res: any) => {
  const d = res.data
  if (d && d.length > 0) {
    serveData.value = d.sort((a, b) => b.price - a.price)
    theBest.nameNumber = getNumbers(serveData.value[serveData.value.length - 1].tempName)
    theBest.price = serveData.value[serveData.value.length - 1].price
  }
})

// 字符串中只提出数字
const getNumbers = (str: string) => {
  const numbers = str.match(/\d+/g)
  return numbers ? numbers.join('') : ''
}

/**
 * 找出面值最大的，最低使用额度满足的代金券
 * @param list
 */
const findMaxPriceCoupon = (list: any[]) => {
  // 过滤出符合条件的对象，无需判断优惠券类型serverType，因为请求的是serverType===1的数据
  const filteredList = list.filter(
    (item) =>
      item.couponType === CouponType.cash &&
      item.vendorCode === vendorCode.value &&
      item.minUsagePrice <= actuallyPrice.value,
  )

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }

  // 在过滤后的列表中找出couponPrice最大的对象
  return filteredList.reduce(
    (max, current) => (current.couponPrice > max.couponPrice ? current : max),
    filteredList[0],
  )
}

/**
 * 找出折扣券中折扣最小的
 * @param list
 */
const findBestDiscountCoupon = (list: any[]) => {
  // 过滤出符合条件的对象
  const filteredList = list.filter(
    (item) => item.couponType === CouponType.discount && item.discount,
  )

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }

  return filteredList.reduce(
    (max, current) => (current.discount < max.discount ? current : max),
    filteredList[0],
  )
}

/**
 * 找出最优优惠券，
 * @param list
 */
const getOptimalCoupon = (list: any) => {
  // console.log(list)

  // 最优代金券列表
  const bestPriceItem = findMaxPriceCoupon(list)
  // console.log('bestPriceItem', bestPriceItem)
  // 折扣券列表
  const bestDiscountItem = findBestDiscountCoupon(list)
  // console.log('bestDiscountItem', bestDiscountItem)

  // 代金券计算出来的实付
  let pricePrice = actuallyPrice.value
  // 折扣券计算出来的实付
  let discountPrice = actuallyPrice.value
  if (bestPriceItem) {
    pricePrice = actuallyPrice.value - bestPriceItem.couponPrice
  }
  if (bestDiscountItem) {
    discountPrice = actuallyPrice.value * bestDiscountItem.discount
  }
  if (bestPriceItem === null && bestDiscountItem === null) {
    couponId.value = null
  }
  if (pricePrice <= discountPrice) {
    // 代金券优惠
    // console.log('bestPriceItem', bestPriceItem)
    couponId.value = bestPriceItem?.couponId
    couponType.value = bestPriceItem?.couponType
    discount.value = bestPriceItem?.discount
    couponPrice.value = bestPriceItem?.couponPrice
  } else {
    // 折扣券优惠
    // console.log('bestDiscountItem', bestDiscountItem)
    couponId.value = bestDiscountItem?.couponId
    couponType.value = bestDiscountItem?.couponType
    discount.value = bestDiscountItem?.discount
    couponPrice.value = bestDiscountItem?.couponPrice
  }
}

/**
 * 选优惠券计算优惠
 */
const getOrderPrice = () =>
  new Promise((resolve, reject) => {
    btnDisable.value = true
    const params: SumbitOrderParams = {
      orderCode: orderCode.value,
    }
    if (couponId.value) {
      params.couponId = couponId.value
    }
    sumbitOrderApi(params)
      .then((res: any) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        startBarCode.value = d.startBarCode
        endBarCode.value = d.endBarCode
        size.value = d.size
        price.value = d.price
        useCount.value = d.useCount
        existCount.value = d.existCount
        total.value = d.total
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice

        if (useCount.value === 0) {
          btnStr.value = '已生成过，前往下载'
        }
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })

const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (agree.value) {
        if (useCount.value !== 0) {
          const url = '/pages/paymentSuccess/makeFilm'
          btnDisable.value = true
          uni.showLoading({
            title: '支付中',
          })

          // #ifdef MP-WEIXIN
          // 微信支付逻辑
          payOrderApi({
            orderCode: orderCode.value,
          })
            .then((resData: any) => {
              if (resData.data.isNeedToPay) {
                wxRequestPayment(resData.data)
                  .then(() => {
                    btnDisable.value = false
                    uni.hideLoading()
                    uni.redirectTo({
                      url,
                    })
                  })
                  .catch(() => {
                    btnDisable.value = false
                    uni.hideLoading()
                  })
              } else {
                btnDisable.value = false
                uni.hideLoading()
                uni.redirectTo({
                  url,
                })
              }
            })
            .catch((err: string) => {
              btnDisable.value = false
              uni.hideLoading()
              catchErrorAndNavigateBack(err)
            })
          // #endif

          // #ifdef MP-TOUTIAO
          // 抖音支付逻辑
          getDouYinOrderPreDataApi({
            orderCode: orderCode.value,
            params: '',
            path: 'pages/index/index',
          }).then((res) => {
            // console.log(res.data)
            if (res.data.isZeroOrder) {
              // 无需支付
              btnDisable.value = false
              uni.hideLoading()
              uni.redirectTo({
                url,
              })
            } else {
              const d = {
                orderCode: orderCode.value,
                ...res.data,
              }
              douYinRequestPayment(d)
                .then(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                })
                .catch(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                })
            }
          })
          // #endif
        } else {
          // 已生成过，前往下载
          uni.switchTab({
            url: '/pages/myFilm/index',
          })
        }
      } else {
        useMsgModalStore
          .confirm({
            title: '温馨提示',
            content: '请先勾选已阅读并同意《付款免责声明》',
          })
          .then(() => {
            uni.pageScrollTo({
              selector: '#agreeElement',
            })
          })
      }
    }
  },
  1000,
  { immediate: true },
)

const toDiscountCouponPage = () => {
  uni.navigateTo({
    url: '/pages/discountCoupon/index?toSelect=true',
  })
}

const thisMonth = () => {
  // Get the current date
  const currentDate = new Date()

  // Get the first day of the month
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)

  // Get the last day of the month
  const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

  // Format the dates to YYYY-MM-DD
  const formatDate = (date) => `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`

  // Create the result string
  return `${formatDate(firstDayOfMonth)} ~ ${formatDate(lastDayOfMonth)}`
}

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :articleIds="[Article.film, Article.platform]" />
  </up-modal>
  <view class="bg-white p-4 flex justify-between items-end">
    <view class="pl-2">
      <view class="o-color-primary text-xl font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="text-sm o-color-aid">{{ DESCRIPTION_STR[descriptionServiceType].typeStr }}</view>
      <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6 text-sm">
        <view class="pt-1">
          <view class="font-bold mb-2">收费标准：</view>
          <view class="text-xs">{{ DESCRIPTION_STR.chargingStandard }}</view>
          <view class="font-bold o-color-danger mt-4 mb-2">优惠期间推行阶梯定价：</view>
          <view class="f-table text-xs">
            <view class="flex justify-between">
              <view class="f-table-title text-center grow-1 mr-1">条码数量</view>
              <view class="f-i-price f-table-title shrink-0 text-center">每张（元）</view>
            </view>
            <view class="f-table-context">
              <view v-for="item in serveData" :key="item.priceTempId" class="flex justify-between">
                <view class="text-center grow-1 py-1 mr-1">{{ item.tempName }}</view>
                <view class="f-i-price shrink-0 py-1 text-center">{{ item.price }}</view>
              </view>
            </view>
          </view>
          <view class="mt-1 text-xs pl-8 o-color-danger">
            <text class="pr-2">*</text>
            优惠时间：{{ thisMonth() }}
          </view>
        </view>
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}收费标准说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <view class="bg-white p-4 rounded-lg mt-3">
      <view class="font-bold mt-2 mb-3">要生成条码的编码：</view>
      <view class="flex items-center">
        <view class="o-barcode-gray-card rounded">{{ startBarCode }}</view>
        <template v-if="endBarCode !== startBarCode">
          <view>~</view>
          <view class="o-barcode-gray-card rounded">{{ endBarCode }}</view>
        </template>
      </view>
      <view class="text-xs mt-2 o-color-aid">
        <text class="o-color-danger">*</text>
        校验码显示为 * 号，条码制作时自动生成。如您已有校验码，所生成校验码也会跟其一致，敬请放心。
      </view>
      <view class="mt-3 text-sm">
        放大系数为：
        <text class="font-bold">{{ size }}</text>
      </view>

      <view class="o-line mt-4 mb-4"></view>
      <view class="mt-1 text-sm">
        共
        <text class="font-bold">{{ total }}</text>
        张，其中
        <text class="font-bold">{{ existCount }}</text>
        张已生成过；
      </view>
      <view class="mt-1 text-sm">
        排除重复后，为
        <text class="font-bold">{{ useCount }}</text>
        张。
      </view>
      <view class="text-xs mt-3 o-color-aid">
        <text class="o-color-danger">*</text>
        重复的条码可在我的条码中，直接下载。
      </view>
      <view class="o-line mt-4 mb-4"></view>
      <view class="flex justify-between text-sm">
        <view>
          <text class="font-bold">{{ useCount }}</text>
          张
          <text>×</text>
          <text class="font-bold pl-1">{{ price }}</text>
          元/张
        </view>
        <view>￥{{ totalPrice }}</view>
      </view>
      <view v-if="theBest.nameNumber" class="f-hot mt-3 py-1 relative">
        <view class="o-color-danger text-sm font-bold">优惠提示：</view>
        <view class="o-color-danger text-sm mt-1">
          <text class="font-bold">{{ theBest.nameNumber }}</text>
          张
          <text>×</text>
          <text class="font-bold pl-1">{{ theBest.price }}</text>
          元/张
        </view>
        <view class="o-color-aid text-xs">
          本月特惠期，一次性购买{{ theBest.nameNumber }}张及以上，仅需{{ theBest.price }}元一张。
        </view>
      </view>
      <view class="flex justify-between text-sm mt-3" @click="toDiscountCouponPage">
        <view>{{ couponTypeStr(couponType) }}</view>
        <view class="flex gap-1">
          <template v-if="couponType === CouponType.cash">
            <view>￥</view>
            <view>{{ couponPrice }}</view>
          </template>
          <discount-box v-if="couponType === CouponType.discount" :size="28" :discount="discount" />
          <view class="o-color-aid pr-1">
            <up-icon name="arrow-right" :size="14"></up-icon>
          </view>
        </view>
      </view>
      <view class="o-line mt-3 mb-3"></view>
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box class="o-color-danger" :price="actuallyPrice" :size="48" />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="flex justify-center items-center mt-6 text-xs">
      <up-checkbox
        usedAlone
        v-model:checked="agree"
        labelSize="12"
        :size="14"
        label="我已阅读并同意"
      ></up-checkbox>
      <view class="o-color-primary" @click.stop="isModalShow = true">《付款免责声明》</view>
    </view>
    <view class="p-11"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary'"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-table {
  width: 500rpx;
  margin: 0 auto;
  border: rgba(220, 225, 234, 0.7) 1px solid;
}

.f-table-title {
  padding: 0.2rem 0;
  background-color: #e8efff;
}

.f-table-context > view:nth-child(even) view {
  background-color: rgba(220, 225, 234, 0.3);
}

.f-i-price {
  $w: 200rpx;

  min-width: $w;
  max-width: $w;
}

.f-hot::after {
  @apply rounded absolute px-2 py-1 top--1 left--2;
  width: 100%;
  height: 100%;

  content: '';
  background-color: rgba(245, 63, 63, 0.05);
  border: 1px dashed var(--wot-color-danger);
}
</style>
