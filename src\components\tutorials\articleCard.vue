<script setup lang="ts">
import { DesignImage } from '@/components/image'

const props = defineProps<{
  data: any
}>()

const toContextPage = (id: number) => {
  uni.navigateTo({
    url: '/pages/tutorials/contextPage?id=' + id,
  })
}
</script>

<template>
  <view
    class="flex bg-white p-2 rounded-lg mb-3 gap-1 text-sm"
    @click="toContextPage(data.articleId)"
  >
    <up-image
      class="shrink-0 rounded overflow-hidden"
      :width="85"
      :height="59"
      :src="data.imageUrl ? data.imageUrl : DesignImage.tutorials.defaultMainImg"
    >
      <template #error>
        <up-image
          class="shrink-0 rounded overflow-hidden"
          :width="85"
          :height="59"
          :src="DesignImage.tutorials.defaultMainImg"
        ></up-image>
      </template>
    </up-image>
    <view class="p-2">
      <text class="o-color-danger" v-if="data.articleFlag === 1">【置顶】</text>
      <text>{{ data.articleTitle }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
