<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的订单',
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { storeToRefs } from 'pinia'
import { allOrderPageApi, AllOrderPageRes, AllOrderPageResData } from '@/service/orderApi'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import { InvoiceState, OrderStatus, PayState } from '@/enums'
import { Color } from '@/enums/colorEnum'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
// const selectValue = ref<AllOrderPageResData[]>([])
const tabsList = [{ name: '全部' }, { name: '待发货' }, { name: '待收货' }, { name: '退款售后' }]
const currentTab = ref(0)
const refresherTriggered = ref(false)
const loading = ref(false)

const getOrderList = () =>
  new Promise((resolve, reject) => {
    loading.value = true // 请求前设置loading状态
    allOrderPageApi({
      // serverType:
      // isInvoiced: getIsInvoiced(),
      orderStutas: getOrderStatus(),
      userId: userId.value,
      payState: PayState.paid,
      groupBy: '',
      needTotalCount: true,
      // orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 50,
    })
      .then((res: AllOrderPageRes) => {
        if (res.data.length === 0) {
          showEmptyIcon.value = true
        } else {
          showEmptyIcon.value = false
        }
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        loading.value = false // 请求完成后重置loading状态
      })
  })

onLoad((options) => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  if (options.tab) {
    switch (options.tab) {
      case 'all':
        currentTab.value = 0
        break
      case 'waitSend':
        currentTab.value = 1
        break
      case 'waitReceive':
        currentTab.value = 2
        break
      case 'refund':
        currentTab.value = 3
        break
      default:
        currentTab.value = 0
        break
    }
  }
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList().then((res: AllOrderPageRes) => {
    list.value = res.data
  })
})

onShow(() => {
  // 订单成功后退触发
  if (orderSuccess.value) {
    orderSuccess.value = false
    page.value = 1
    // 清空数据
    list.value = []
    orderCode.value = []
    orderObjList.value = []
    getOrderList()
      .then((res: AllOrderPageRes) => {
        list.value = res.data
      })
      .catch(() => {
        uni.showToast({
          title: '获取数据失败',
          icon: 'none',
        })
      })
  }
})

// scroll-view 下拉刷新
const onRefresh = () => {
  refresherTriggered.value = true
  showEmptyIcon.value = false
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
      })
    })
    .finally(() => {
      refresherTriggered.value = false
    })
}

// scroll-view 上拉加载更多
const onLoadMore = () => {
  if (!showEmptyIcon.value && !loading.value) {
    page.value = page.value + 1
    getOrderList()
      .then((res: AllOrderPageRes) => {
        if (res && res.data.length > 0) {
          list.value = [...list.value, ...res.data]
        } else {
          // 没有更多数据
          showEmptyIcon.value = true
        }
      })
      .catch(() => {
        // 加载失败时回退页码
        page.value--
        uni.showToast({
          title: '加载更多失败',
          icon: 'none',
        })
      })
  }
}

const getOrderStatus = () => {
  switch (currentTab.value) {
    case 0:
      // all
      return []
    case 1:
      // waitSend
      return [OrderStatus.waitingSend]
    case 2:
      // waitReceive
      return [OrderStatus.waitingReceive]
    case 3:
      // refund
      return [OrderStatus.afterSale, OrderStatus.afterSaleEnd]
    default:
      return []
  }
}

const getOrderStatusStr = (orderStatus: OrderStatus) => {
  switch (orderStatus) {
    case OrderStatus.waitingSend:
      return '待发货'
  }
}

// 原来的滚到页面底部加载更多已由scroll-view的scrolltolower事件替代

const handleSelect = (d: AllOrderPageResData) => {
  // 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
  if (!d.invoiceState) {
    if (orderCode.value.includes(d.orderCode)) {
      orderCode.value = orderCode.value.filter((item) => item !== d.orderCode)
      // selectValue.value = selectValue.value.filter((item) => item.orderCode !== d.orderCode)
    } else {
      orderCode.value.push(d.orderCode)
      // selectValue.value.push(d)
    }
  }
}

const getStateColor = (status: InvoiceState) => {
  switch (status) {
    case InvoiceState.failed:
      return 'color-red'
    case InvoiceState.applying:
      return 'color-primary'
    case InvoiceState.done:
      return 'color-green'
    default:
      return 'text-gray'
  }
}

const handleSubmit = () => {
  if (orderCode.value.length > 0) {
    const length = orderCode.value.length
    let price = 0
    const selectValue = list.value.filter((item) => orderCode.value.includes(item.orderCode))
    selectValue.forEach((subItem) => {
      price += subItem.actuallyPrice
    })
    price = Number(price.toFixed(2))
    orderObjList.value = selectValue.map((item) => {
      return {
        orderId: item.orderId,
        serverType: item.serverType,
      }
    })
    uni.navigateTo({
      url: '/pages/myOrder/invoiceInfo?length=' + length + '&price=' + price,
    })
  }
}

const getDisable = (invoiceState: number) => {
  return invoiceState !== 0 && invoiceState !== null
}

const handleFilter = () => {
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    })
}

const handleViewLogistics = (orderCode: string) => {
  uni.navigateTo({
    url: '/pages/myOrder/logisticsDetail?orderCode=' + orderCode,
  })
}
</script>
<template>
  <up-sticky bgColor="#fff">
    <up-tabs
      :lineColor="Color.primary"
      :lineWidth="90"
      :scrollable="false"
      :list="tabsList"
      v-model:current="currentTab"
      @change="handleFilter"
    ></up-tabs>
  </up-sticky>
  <scroll-view
    :scroll-y="true"
    :refresher-enabled="true"
    :refresher-triggered="refresherTriggered"
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore"
    :style="{ height: 'calc(100vh - 44px)' }"
  >
    <div class="px-3">
      <view
        class="bg-white p-3 rounded-sm mt-2"
        v-for="item in list"
        :key="`${item.serverType}${item.orderId}`"
      >
        <view class="flex items-center justify-between">
          <view>
            <view class="o-color-aid text-xs">成交时间：{{ item.payDate }}</view>
            <view class="o-color-aid text-xs">订单编号：{{ item.orderCode }}</view>
          </view>
          <view class="font-bold text-primary">
            {{ item.orderStutasStr }}
          </view>
        </view>
        <view class="mt-2 flex space-x-2">
          <image class="f-img" mode="aspectFill" :src="item.otherOrderParamDTO?.skuImg" />
          <view>
            <view class="truncate">{{ item.otherOrderParamDTO?.title }}</view>
            <view class="text-gray text-sm">{{ item.otherOrderParamDTO?.orderContent }}</view>
          </view>
        </view>
        <view class="flex justify-between items-end gap-3">
          <view></view>
          <view class="flex shrink-0 justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">实付：</view>
              <price-box :price="item.actuallyPrice" :size="36" />
            </view>
          </view>
        </view>
        <view class="o-line mt-2 mb-2"></view>
        <view class="flex justify-end">
          <view
            class="px-5 py-2 text-sm center bg-gray-100 rounded-sm"
            @click="handleViewLogistics(item.orderCode)"
          >
            查看物流
          </view>
        </view>
      </view>
      <up-empty
        v-if="list?.length === 0"
        icon="https://wx.gs1helper.com/images/common/search.png"
        text="暂无订单"
      ></up-empty>
    </div>
    <view class="w-full o-color-aid text-center" v-if="showEmptyIcon">- 已经到底了 -</view>
  </scroll-view>
</template>

<style lang="scss" scoped>
.f-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
