<route lang="json5">
{
  style: {
    navigationBarTitleText: '开票信息编辑',
  },
}
</route>
<script lang="ts" setup>
import { useInvoiceInfoHook } from '@/components/invoice/invoiceInfoHook'
import { invoiceTempCreateApi, invoiceTempUpdateApi } from '@/service/invoiceTempleApi'
import { storeToRefs } from 'pinia'
import { invoiceTempleStore } from '@/store/invoiceTempleStore'
import { useUserStore } from '@/store/user'
import { Article } from '@/enums'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { msgModalStore } from '@/store/msgModalStore'

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useInvoiceTempleStore = invoiceTempleStore()
const { invoiceData, invoiceTempId } = storeToRefs(useInvoiceTempleStore)

const operationType = ref('add')
const agree = ref(false)
const isModalShow = ref(false)

const {
  model,
  form,
  validatePhoneNumber,
  validateMail,
  validateIdentificationNumber,
  validateBankCode,
} = useInvoiceInfoHook()

const rules = {
  companyName: [{ required: true, message: '请输入发票抬头', trigger: ['blur'] }],
  creditCode: [
    {
      required: true,
      validator: validateIdentificationNumber,
      message: '请输入正确纳税人识别号',
      trigger: ['blur'],
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['blur'],
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.email(value)
      },
      message: '请输入正确的邮箱地址',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  contact: [{ required: true, message: '请输入联系人名称', trigger: ['blur'] }],
  contactPhone: [
    {
      required: true,
      message: '请输入联系手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  bankCode: [
    {
      required: false,
      validator: validateBankCode,
      message: '请输入正确银行帐号',
      trigger: ['blur'],
    },
  ],
}

onLoad((option: any) => {
  operationType.value = option.type
  if (option.type === 'edit') {
    model.contact = invoiceData.value.contact
    model.contactPhone = invoiceData.value.contactPhone
    model.companyName = invoiceData.value.companyName
    model.creditCode = invoiceData.value.creditCode
    model.email = invoiceData.value.email
    model.bank = invoiceData.value.bank
    model.bankCode = invoiceData.value.bankCode
    model.phone = invoiceData.value.phone
    model.address = invoiceData.value.address
  }
})

const toBack = () => {
  uni.showToast({
    title: '保存成功',
  })
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}

const handleSubmit = () => {
  form.value
    .validate()
    .then((valid) => {
      console.log('valid', valid)
      if (valid) {
        if (agree.value) {
          switch (operationType.value) {
            case 'add':
              invoiceTempCreateApi({
                address: model.address,
                bank: model.bank,
                bankCode: model.bankCode,
                companyName: model.companyName,
                contact: model.contact,
                contactPhone: model.contactPhone,
                creditCode: model.creditCode,
                email: model.email,
                phone: model.phone,
                userId: userId.value,
              }).then(() => {
                toBack()
              })
              break
            case 'edit':
              invoiceTempUpdateApi({
                address: model.address,
                bank: model.bank,
                bankCode: model.bankCode,
                companyName: model.companyName,
                contact: model.contact,
                contactPhone: model.contactPhone,
                creditCode: model.creditCode,
                email: model.email,
                phone: model.phone,
                userId: userId.value,
                invoiceTempId: invoiceTempId.value,
              }).then(() => {
                toBack()
              })
              break
          }
        } else {
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: '请先勾选已阅读并同意《用户服务协议》《隐私政策》',
            })
            .then(() => {
              uni.pageScrollTo({
                selector: '#agreeElement',
              })
            })
        }
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :articleIds="[Article.privacy]" />
  </up-modal>
  <view class="p-4 pb-6">
    <view class="bg-white rounded-lg overflow-hidden pl-4">
      <up-form ref="form" :model="model" :rules="rules" class="mt-2 ml-2" labelWidth="100">
        <up-form-item required label="发票抬头" prop="companyName">
          <up-input
            v-model="model.companyName"
            clearable
            border="bottom"
            placeholder="企业名称"
          ></up-input>
        </up-form-item>
        <up-form-item required label="纳税人识别号" prop="creditCode">
          <up-input
            v-model="model.creditCode"
            clearable
            :maxlength="20"
            border="bottom"
            placeholder=""
          ></up-input>
        </up-form-item>
        <up-form-item required label="电子邮箱" prop="email">
          <up-input
            v-model="model.email"
            clearable
            border="bottom"
            placeholder="用于接收电子发票"
          ></up-input>
        </up-form-item>
        <up-form-item required label="联系人" prop="contact">
          <up-input v-model="model.contact" clearable border="bottom" placeholder=""></up-input>
        </up-form-item>
        <up-form-item required label="联系手机号" prop="contactPhone">
          <up-input
            v-model="model.contactPhone"
            clearable
            :maxlength="11"
            border="bottom"
            placeholder=""
          >
            <template #suffix>
              <text class="text-gray text-xs">{{ model.contactPhone.length }}/11</text>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item label="开户银行" prop="bank">
          <up-input v-model="model.bank" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
        <up-form-item label="银行账号" prop="bankCode">
          <up-input
            v-model="model.bankCode"
            clearable
            border="bottom"
            placeholder="选填"
          ></up-input>
        </up-form-item>
        <up-form-item label="注册地址" prop="address">
          <up-input v-model="model.address" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
        <up-form-item label="注册电话" prop="phone">
          <up-input v-model="model.phone" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
      </up-form>
    </view>
  </view>
  <view
    id="agreeElement"
    class="flex justify-center items-center text-xs pb-20"
    @click="agree = !agree"
  >
    <up-checkbox
      usedAlone
      v-model:checked="agree"
      labelSize="12"
      :size="14"
      label="我已阅读并同意"
    ></up-checkbox>
    <view class="o-color-primary" @click.stop="isModalShow = true">《付款免责声明》</view>
  </view>
  <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
    <view
      class="p-3 flex-grow-1 o-bg-primary flex items-center justify-center color-white font-bold rounded-lg"
      @click="handleSubmit"
    >
      保存
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
