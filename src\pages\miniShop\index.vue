<route lang="json5">
{
  style: {
    navigationBarTitleText: '二维码微站',
  },
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import { createQr<PERSON>rder<PERSON><PERSON>, CreateQrOrderParams, memberLevelTempListApi } from '@/service/orderApi'
import PriceBox from '@/components/Price/PriceBox.vue'
import { getMiniShopTempTypeStr } from '@/utils'
import { miniShopOrderStore } from '@/store/orderStore/miniShopOrderStore'
import MiniShopStep from '@/components/miniShop/miniShopStep.vue'
import { DesignImage } from '@/components/image'
import ServerSwiper from '@/components/serverSwiper.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { msgModalStore } from '@/store/msgModalStore'
import { FromPlatform } from '@/enums'

/* defineOptions({
  name: 'MakeFilm',
}) */

const useMsgModalStore = msgModalStore()

const useOrderStore = miniShopOrderStore()
const { orderCode, tempName, tempType, capacity, flow } = storeToRefs(useOrderStore)

const userStore = useUserStore()
userStore.login()

const qrImg = ref('')
const loading = ref(false)
const isShowStep = ref(true)
const serveData = ref<any[]>([])
const selectTempId = ref()

qrImg.value = 'https://wx.gs1helper.com/images/miniShop_Qr_template.png'

memberLevelTempListApi().then((res: any) => {
  serveData.value = res.data
})

const handleSubmit = () => {
  // couponId
  if (!selectTempId.value) {
    useMsgModalStore.alert({ title: '请选择方案' })
    return
  }
  loading.value = true
  uni.showLoading({ title: '订单提交中...' })
  const params: CreateQrOrderParams = {
    number: 1,
    tempId: selectTempId.value,
    userId: useUserStore().userId,
  }
  // #ifdef MP-WEIXIN
  params.fromTo = FromPlatform.wx
  // #endif
  // #ifdef MP-TOUTIAO
  params.fromTo = FromPlatform.douYin
  // #endif
  createQrOrderApi(params)
    .then((res) => {
      const d = res.data
      orderCode.value = d.orderCode
      tempType.value = d.tempType
      capacity.value = d.capacity
      flow.value = d.flow
      loading.value = false
      uni.hideLoading()
      uni.navigateTo({
        url: '/pages/orderConfirm/miniShop',
      })
    })
    .catch(() => {
      loading.value = false
      uni.hideLoading()
    })
}

const handleSelect = (d: any) => {
  selectTempId.value = d.tempId
  tempName.value = d.tempName
}

const handlePreview = () => {
  uni.navigateTo({
    url:
      '/pages/miniShop/viewWebPage?url=' +
      encodeURIComponent('https://qr.gs1helper.com/gtin/06900000000007/gs1minishopcode/1005'),
  })
}
</script>
<template>
  <server-swiper :carouses-list="DesignImage.miniShop.carouses" />
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg center flex-col">
      <view class="font-bold text-sm mb-2 w-full text-left">示例展示：</view>
      <view class="o-border px-6 py-2 rounded-lg text-sm color-primary mb-2" @click="handlePreview">
        点击浏览示例
      </view>
      <up-image :width="200" :height="200" :src="qrImg" alt="二维码" :showMenuByLongpress="true" />
      <view class="text-xs text-gray">或长按保存后，用浏览器扫码</view>
    </view>
  </view>

  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <mini-shop-step v-if="isShowStep" />
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}开通步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>

    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view class="mt-4" id="targetElement">
        <up-radio-group class="o-vf-up-radio-group" v-model="selectTempId" placement="column">
          <up-radio
            v-for="(item, index) in serveData"
            :key="index"
            :name="item.tempId"
            @change="handleSelect(item)"
          >
            <template #label>
              <view class="f-serve flex justify-between mt-2 items-center">
                <view class="shrink-0">
                  <view>{{ item.tempName }}</view>
                  <view class="text-gray">含{{ item.capacity }}G存储空间</view>
                </view>
                <view class="shrink-0 inline-block flex flex-col items-end">
                  <view class="flex items-baseline">
                    <price-box
                      :price="item.discountPrice"
                      notShowDecimal
                      :size="40"
                      class="color-red"
                    />
                    <view class="text-gray text-xs">
                      /{{ getMiniShopTempTypeStr(item.tempType) }}
                    </view>
                  </view>
                  <price-box
                    v-if="Math.abs(item.discountPrice - item.originalPrice) > Number.EPSILON"
                    :price="item.originalPrice"
                    cancel
                    thin
                    notShowDecimal
                    :size="32"
                    class="text-gray"
                  />
                </view>
              </view>
            </template>
          </up-radio>
        </up-radio-group>
      </view>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-serve {
  width: 540rpx;
}
</style>
