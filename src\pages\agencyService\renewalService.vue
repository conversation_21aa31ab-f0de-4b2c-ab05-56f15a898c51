<route lang="json5">
{
  style: {
    navigationBarTitleText: '条码续约续展',
  },
}
</route>

<script lang="ts" setup>
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import RenewalStep from '@/components/agencyService/renewalStep.vue'
import RenewalTip from '@/components/agencyService/renewalTip.vue'
import { toAgencyPath } from '@/utils'
import { ServerType } from '@/enums'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { existUncompletedRenewal } = storeToRefs(userStore)

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit } =
  useCreateOrder({
    orderConfirmUrl: '/pages/orderConfirm/default',
    paymentSuccessUrl: '/pages/paymentSuccess/agencyService',
  })
</script>
<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <view class="font-bold text-sm mb-2">条码过期怎么办：</view>
        <RenewalTip class="text-sm" />
        <view class="o-line"></view>
        <RenewalStep />
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}办理说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold text-sm mb-2 color-red">什么情况需要加办“变更”：</view>
      <view class="o-p text-sm">
        但凡以下任意一项：“企业名称、注册地址、法人、企业类型”，都需要加办“变更”。
      </view>
      <view class="o-p text-sm">
        请自行核对是否变更过上述信息，避免办理失败，浪费时间导致逾期。不知是否有变更者，可统一办理变更业务。
      </view>
    </view>
    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view class="mt-4" id="targetElement">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view class="flex gap-1">
        <view
          class="py-3 flex-grow-1 flex items-center justify-center font-bold rounded-lg"
          :class="
            existUncompletedRenewal
              ? 'bg-white o-color-primary o-border'
              : 'o-bg-primary color-white'
          "
          @click="handleSubmit"
        >
          {{ existUncompletedRenewal ? '继续下单' : '提交方案' }}
        </view>
        <view
          v-if="existUncompletedRenewal"
          class="py-3 flex-grow-1 o-bg-primary color-white rounded-lg flex justify-center items-baseline"
          @click="toAgencyPath(ServerType.renewalService)"
        >
          <text class="text-xs">您有未完成的业务，</text>
          <text class="font-bold">前往办理</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
