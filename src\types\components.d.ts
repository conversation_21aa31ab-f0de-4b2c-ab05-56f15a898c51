/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ArticleCard: typeof import('./../components/tutorials/articleCard.vue')['default']
    CouponCard: typeof import('./../components/Price/CouponCard.vue')['default']
    CsBtnLeftButton: typeof import('./../components/customer/csBtnLeftButton.vue')['default']
    CsLongButton: typeof import('./../components/customer/csLongButton.vue')['default']
    CsRoundButton: typeof import('./../components/customer/csRoundButton.vue')['default']
    DiscountBox: typeof import('./../components/Price/DiscountBox.vue')['default']
    FreeDownPage: typeof import('./../components/myFilm/freeDownPage.vue')['default']
    GoodsCard: typeof import('./../components/shopMall/goodsCard.vue')['default']
    LogoTagCard: typeof import('./../components/logoTagCard.vue')['default']
    MallSwiper: typeof import('./../components/mallSwiper.vue')['default']
    MiniShopStep: typeof import('./../components/miniShop/miniShopStep.vue')['default']
    ModifyStep: typeof import('./../components/agencyService/modifyStep.vue')['default']
    ModifyTip: typeof import('./../components/agencyService/modifyTip.vue')['default']
    MsgModal: typeof import('./../components/msgModal.vue')['default']
    NeedPhoneForm: typeof import('./../components/needPhoneForm.vue')['default']
    OrderDownPage: typeof import('./../components/myFilm/orderDownPage.vue')['default']
    PaidDisclaimer: typeof import('./../components/agreement/paidDisclaimer.vue')['default']
    PriceBox: typeof import('./../components/Price/PriceBox.vue')['default']
    RegisterStep: typeof import('./../components/agencyService/registerStep.vue')['default']
    RenewalStep: typeof import('./../components/agencyService/renewalStep.vue')['default']
    RenewalTip: typeof import('./../components/agencyService/renewalTip.vue')['default']
    ReportStep: typeof import('./../components/infoReport/reportStep.vue')['default']
    ReportSuccessStep: typeof import('./../components/infoReport/reportSuccessStep.vue')['default']
    ServerRadioGroup: typeof import('./../components/serverRadioGroup.vue')['default']
    ServerSwiper: typeof import('./../components/serverSwiper.vue')['default']
    ServerTitle: typeof import('./../components/serverTitle.vue')['default']
  }
}
