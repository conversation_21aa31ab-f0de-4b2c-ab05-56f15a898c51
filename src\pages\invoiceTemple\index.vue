<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '开票信息',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import { OrderDirection } from '@/enums/httpEnum'
import { invoiceTempDelApi, invoiceTempleApi, InvoiceTempPageRes } from '@/service/invoiceTempleApi'
import { invoiceTempleStore } from '@/store/invoiceTempleStore'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { msgModalStore } from '@/store/msgModalStore'

const useMsgModalStore = msgModalStore()
const userStore = useUserStore()
const { userId, defaultInvoiceTempId } = storeToRefs(userStore)
const useInvoiceTempleStore = invoiceTempleStore()
const { invoiceData, invoiceTempId } = storeToRefs(useInvoiceTempleStore)

const toSelect = ref(false)

const { loading, error, data, run } = useRequest<InvoiceTempPageRes>(() =>
  invoiceTempleApi({
    companyName: '',
    creditCode: '',
    userId: userId.value,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'invoiceTempId',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
  }),
)

onLoad((option: any) => {
  toSelect.value = option.toSelect === 'true'
})

onShow(() => {
  run()
})

onPullDownRefresh(() => {
  run().finally(() => {
    uni.stopPullDownRefresh()
  })
})

const handleEdit = (item: any) => {
  console.log(item)
  invoiceTempId.value = item.invoiceTempId
  invoiceData.value = {
    address: item.address,
    bank: item.bank,
    bankCode: item.bankCode,
    companyName: item.companyName,
    contact: item.contact,
    contactPhone: item.contactPhone,
    creditCode: item.creditCode,
    email: item.email,
    phone: item.phone,
  }
  uni.navigateTo({
    url: '/pages/invoiceTemple/editor?type=edit',
  })
}

const handleAdd = () => {
  uni.navigateTo({
    url: '/pages/invoiceTemple/editor?type=add',
  })
}

const handleDelete = (item: any) => {
  useMsgModalStore
    .confirm({
      title: '删除开票信息？',
    })
    .then(() => {
      invoiceTempDelApi({
        invoiceTempId: item.invoiceTempId,
      }).then(() => {
        run()
      })
    })
}

const handleSelect = (data: any) => {
  if (toSelect.value) {
    defaultInvoiceTempId.value = data.invoiceTempId
    uni.navigateBack()
  }
}
</script>
<template>
  <view class="p-4 pb-10">
    <view v-if="toSelect" class="py-3">请选择发票信息：</view>
    <view
      class="bg-white mb-3 p-2 flex-grow-1 flex items-center justify-center rounded-lg text-sm gap-1"
      @click="handleAdd"
    >
      <up-icon name="plus" :size="14"></up-icon>
      新增
    </view>
    <view
      v-for="item in data"
      class="bg-white text-sm p-4 rounded-lg mb-3"
      :key="item.invoiceTempId"
      @click="handleSelect(item)"
    >
      <view class="f-box">
        <view class="f-label o-color-aid">发票抬头/企业名称：</view>
        <view>{{ item.companyName }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">纳税人识别号：</view>
        <view>{{ item.creditCode }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">邮箱：</view>
        <view>{{ item.email }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">联系人：</view>
        <view>{{ item.contact }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">联系电话：</view>
        <view>{{ item.contactPhone }}</view>
      </view>
      <view v-if="item.bank != ''" class="f-box">
        <view class="f-label o-color-aid">开户银行：</view>
        <view>{{ item.bank }}</view>
      </view>
      <view v-if="item.bankCode != ''" class="f-box">
        <view class="f-label o-color-aid">银行账号：</view>
        <view>{{ item.bankCode }}</view>
      </view>
      <view v-if="item.address != ''" class="f-box">
        <view class="f-label o-color-aid">注册地址：</view>
        <view>{{ item.address }}</view>
      </view>
      <view v-if="item.phone != ''" class="f-box">
        <view class="f-label o-color-aid">注册电话：</view>
        <view>{{ item.phone }}</view>
      </view>
      <!-- <view class="o-label-line mt-4 mb-4"></view> -->
      <view class="flex gap-2 items-center">
        <view
          class="f-delete o-bg-no mt-3 p-2 flex items-center justify-center rounded-lg text-sm"
          @click.stop="handleDelete(item)"
        >
          <up-icon name="trash" :size="18"></up-icon>
        </view>
        <view
          class="o-bg-no mt-3 p-2 flex-grow-1 flex items-center justify-center rounded-lg text-sm"
          @click.stop="handleEdit(item)"
        >
          <up-icon name="edit-pen" :size="18"></up-icon>
          <text class="ml-1">编辑</text>
        </view>
      </view>
    </view>
    <view class="p-3"></view>
    <up-empty
      class="pb-4"
      v-if="data?.length === 0"
      icon="https://wx.gs1helper.com/images/common/content.png"
      text="请添加开票信息"
    ></up-empty>
  </view>
</template>

<style lang="scss" scoped>
.f-box {
  display: flex;
  justify-content: space-between;
  margin-top: 0.4rem;
}

.f-label {
  min-width: 5rem;
}

.f-delete {
  width: 2rem;
}
</style>
