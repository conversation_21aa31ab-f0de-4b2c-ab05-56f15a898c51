<route lang="json5">
{
  style: {
    navigationBarTitleText: '注册续展变更办理',
  },
}
</route>

<script lang="ts" setup>
import { useToPath } from '@/hooks/useToPath'
import { BaseProjectType, PROJECT_3 } from '@/components/descriptionStr'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { toAgencyPath } from '@/utils'
import { ServerType } from '@/enums'

const userStore = useUserStore()
const { existUncompletedRegister, existUncompletedRenewal, existUncompletedChange } =
  storeToRefs(userStore)
const { toPath } = useToPath()

const whichService = (serviceTypeStr: BaseProjectType['descriptionServiceType']) => {
  switch (serviceTypeStr) {
    case 'renewalService':
      if (existUncompletedRenewal.value) {
        return ServerType.renewalService
      }
      break
    case 'registerService':
      if (existUncompletedRegister.value) {
        return ServerType.registerService
      }
      break
    case 'modifyService':
      if (existUncompletedChange.value) {
        return ServerType.modifyService
      }
      break
  }
  return false
}
</script>
<template>
  <view class="p-4">
    <view class="font-bold mb-2">请选择业务：</view>
    <view
      v-for="(item, index) in PROJECT_3"
      :key="index"
      class="mb-2 bg-white pl-4 pr-2 py-2 rounded-lg"
      @click="toPath(item, 'navigateTo')"
    >
      <view class="flex items-center gap-3">
        <view class="grow flex flex-col gap-1">
          <view class="text-base font-bold">{{ item.title }}</view>
          <view>
            <view
              class="o-tag o-color-primary rounded float-left"
              :style="{ color: item.tagStrColor, background: item.tagBgColor }"
            >
              {{ item.typeStr }}
            </view>
          </view>
          <view class="text-xs o-color-aid">{{ item.description }}</view>
        </view>
        <view class="o-color-aid pr-1">
          <up-icon name="arrow-right" :size="14"></up-icon>
        </view>
      </view>
      <template v-if="whichService(item.descriptionServiceType)">
        <view class="o-line mt-4 mb-4"></view>
        <view
          class="o-bg-primary mt-3 py-1 flex-grow-1 flex items-baseline justify-center color-white rounded-lg"
          @click.stop="toAgencyPath(whichService(item.descriptionServiceType))"
        >
          <text class="text-sm">您有未完成的业务，</text>
          <text class="font-bold">前往办理</text>
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
