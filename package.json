{"name": "shopping_mall_wx", "type": "commonjs", "version": "0.0.1", "description": "", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:mp": "uni -p mp-weixin", "build:mp": "uni build -p mp-weixin", "uvm": "npx @dcloudio/uvm@latest", "type-check": "vue-tsc --noEmit"}, "lint-staged": {"**/*.{vue,html,cjs,json,md,scss,css,txt}": ["prettier --write --cache"], "**/*.{js,ts}": ["oxlint --fix", "prettier --write --cache"], "!**/{node_modules,dist}/**": []}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@tanstack/vue-query": "^5.62.16", "abortcontroller-polyfill": "^1.7.8", "clipboard": "^2.0.11", "dayjs": "1.11.10", "debounce": "^2.2.0", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "uview-plus": "^3.4.40", "vue": "^3.5.15", "vue-i18n": "9.1.9", "z-paging": "^2.8.7"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/types": "^3.4.15", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@douyin-microapp/typings": "^1.3.1", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@iconify-json/carbon": "^1.2.4", "@rollup/rollup-darwin-x64": "^4.28.0", "@types/node": "^20.17.57", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-types": "1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.8", "@uni-helper/vite-plugin-uni-pages": "0.2.28", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@uni-ku/bundle-optimizer": "^1.3.3", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.5.16", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.21", "commitlint": "^19.8.1", "czg": "^1.11.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^9.33.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "openapi-ts-request": "^1.1.2", "oxlint": "^0.1.0", "postcss": "^8.5.4", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.77.8", "terser": "^5.40.0", "typescript": "^5.8.3", "unocss": "65.4.2", "unplugin-auto-import": "^0.17.8", "vite": "6.3.5", "vite-plugin-restart": "^0.4.2", "vitepress": "^1.6.3", "vitepress-plugin-llms": "^1.3.4", "vue-tsc": "^2.2.10"}, "pnpm": {"onlyBuiltDependencies": ["@uni-helper/unocss-preset-uni", "core-js", "es5-ext", "esbuild", "unrs-resolver", "vue-demi"]}}