<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{ style: { navigationBarTitleText: '支付成功' } }
</route>
<script lang="ts" setup>
import { orderStore } from '@/store/orderStore'
import { storeToRefs } from 'pinia'
import PriceBox from '@/components/Price/PriceBox.vue'
import { getCouponApi } from '@/service/orderApi'
import CouponCard from '@/components/Price/CouponCard.vue'
import { ServerType } from '@/enums'
import RenewalStep from '@/components/agencyService/renewalStep.vue'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import ModifyStep from '@/components/agencyService/modifyStep.vue'

const useOrderStore = orderStore()
const {
  orderCode,
  tempName,
  actuallyPrice,
  serverType: storeServerType,
} = storeToRefs(useOrderStore)

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const serverType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref(0)
const showCoupon = ref(false)

getCouponApi({ orderCode: orderCode.value }).then((res: any) => {
  console.log(res)
  const d = res.data
  if (d.couponId) {
    couponName.value = d.couponName
    couponPrice.value = d.couponPrice
    couponType.value = d.couponType
    description.value = d.description
    discount.value = d.discount
    serverType.value = d.serverType
    expirySeconds.value = d.expirySeconds
    vendorCode.value = d.vendorCode
    showCoupon.value = true
  }
})

const toHandlingService = () => {
  uni.switchTab({
    // 这样才有后退打底
    url: '/pages/userPage/index',
    success: () => {
      uni.navigateTo({
        url: '/pages/myAgency/index?serverType=' + storeServerType.value + '&skipAuth=1',
      })
    },
  })
}
</script>
<template>
  <view class="px-4 pt-4 pb-10">
    <view class="o-color-primary pl-3 text-xl font-bold">订单支付成功！</view>
    <view v-if="showCoupon" class="o-color-primary pl-3 text-sm">恭喜获得一张折扣券</view>
    <view class="bg-white rounded-lg mt-2 p-4">
      <view class="o-color-aid text-xs">订单编号：{{ orderCode }}</view>
      <view class="o-color-aid text-xs mt-2">服务类型：</view>
      <view class="flex items-center mb-2">{{ tempName }}</view>
      <view class="flex items-baseline justify-end">
        <view class="text-sm">实付：</view>
        <price-box :price="actuallyPrice" :size="48" />
      </view>
    </view>
    <!--    <NeedPhoneForm />-->
    <view class="bg-white rounded-lg mt-3">
      <view class="p-4">
        <RegisterStep v-if="storeServerType === ServerType.registerService" />
        <RenewalStep v-if="storeServerType === ServerType.renewalService" />
        <ModifyStep v-if="storeServerType === ServerType.modifyService" />
        <view
          class="p-3 mt-4 o-bg-primary flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
          @click="toHandlingService"
        >
          前往提交资料
        </view>
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot w-full mb-4"></view>
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}
</style>
