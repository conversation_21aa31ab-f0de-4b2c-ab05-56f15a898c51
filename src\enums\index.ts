// 图片上传最大大小 3M
export const UPLOAD_IMG_MAXSIZE = 1024 * 1024 * 3
export const BASE64IMG_PREFIX = 'data:image/jpeg;base64,'

export enum BarType {
  EAN13 = 'EAN13',
  ITF14 = 'ITF14',
}

/**
 * 支付状态
 * @param new 新建：-1
 * @param notPay 未支付：0
 * @param paid 已支付：1
 * @param failed 支付失败：2
 */
export enum PayState {
  new = -1,
  notPay = 0,
  paid = 1,
  failed = 2,
}

/**
 * 协议id
 * @param privacy 隐私协议
 * @param platform 平台免责声明
 * @param design 设计免责声明
 * @param film 付费免责声明（条码制作）
 * @param infoReport 付费免责声明（产品编码信息通报）
 * @param miniShop 付费免责声明（二维码微站）
 * @param labelPrint 付费免责声明（标签印刷）
 * @param agencyService 付费免责声明（代办服务）
 */
export enum Article {
  privacy = 78, // 隐私协议
  platform = 77, // 平台免责声明
  design = 76, // 付款免责声明（设计）
  film = 75, // 付费免责声明（条码制作）
  infoReport = 74, // 付费免责声明（产品编码信息通报）
  miniShop = 79, // 付费免责声明（二维码微站）
  labelPrint = 80, // 付费免责声明（标签印刷）
  agencyService = 82, // 付费免责声明（业务代办）
}

/**
 * 优惠券类型
 * @param no 默认值0，前端用于默认为优惠券
 * @param cash 代金券1
 * @param discount 折扣券2
 */
export enum CouponType {
  no = 0, // 前端用于默认为优惠券
  cash = 1, // 代金券
  discount = 2, // 折扣券
}
export const couponTypeStr = (couponType: CouponType) => {
  switch (couponType) {
    case 1:
      return '代金券'
    case 2:
      return '折扣券'
    default:
      return '优惠券'
  }
}

/**
 * 服务类型
 */
export enum ServerType {
  makeFilm = 1, // 条码制作
  infoReport = 2, // 产品信息上报
  designServer = 3, // 设计
  labelPrint = 4, // 标签
  storeCode = 5, // 店内码
  renewalService = 6, // 续展
  // labelDesign = 7, // 标签设计
  // printer = 8, // 打印机
  importedGoods = 9, // 进口商品报备
  registerService = 10, // 注册
  miniShop = 11, // 二维码微站
  modifyService = 12, // 变更
}
const serverTypeMap: Map<ServerType, string> = new Map([
  [ServerType.makeFilm, '条码制作'],
  [ServerType.infoReport, '产品通报'],
  [ServerType.designServer, '包装设计'],
  [ServerType.labelPrint, '标签印刷'],
  [ServerType.storeCode, '条码制作'], // 店内码
  [ServerType.miniShop, '二维码微站'],
  [ServerType.renewalService, '条码续展'],
  [ServerType.importedGoods, '进口商品报备'],
  [ServerType.registerService, '条码会员注册'],
  [ServerType.modifyService, '条码会员变更'],
])

export const getServerTypeStr = (serverType: ServerType) => {
  return serverTypeMap.get(serverType) || ''
}

/**
 * 商品状态
 * @param cache 缓存商品：0
 * @param unListed 未上架：1
 * @param listed 已上架：2
 */
export enum GoodsStatus {
  cache = 0,
  unListed = 1,
  listed = 2,
}

/**
 * 单一/多规格
 * @param single 单规格：1
 * @param multi 多规格：2
 */
export enum SpecType {
  single = 1,
  multi = 2,
}

export enum GoodsIdEnum {
  printer = 1,
  labelPrint = 3,
}

/**
 * 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
 */
export enum InvoiceState {
  failed = -1, // 废票
  notApply = 0, // 未申请
  done = 1, // 已开票
  applying = 2, // 已申请
}

export enum FromPlatform {
  wx = 1,
  douYin = 2,
}

/**
 * 订单状态
 * @param waitingSend 0 待发货
 * @param waitingReceive 1 待收货
 * @param waitingEvaluate 2 已收货，待评价
 * @param done 3 已完成
 * @param afterSale 4 售后申请中
 * @param afterSaleEnd 5 售后结束
 */
export enum OrderStatus {
  waitingSend = 0, // 待发货
  waitingReceive = 1, // 待收货
  waitingEvaluate = 2, // 已收货，待评价
  done = 3, // 已完成
  afterSale = 4, // 售后申请中
  afterSaleEnd = 5, // 售后结束
}
