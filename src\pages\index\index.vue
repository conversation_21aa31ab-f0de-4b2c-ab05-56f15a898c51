<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '迅码智联',
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
  },
}
</route>

<script lang="ts" setup>
import { mainSale } from '@/components/descriptionStr'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { DesignImage, PublicImgPath } from '@/components/image'
import { wxmalinkSignUrlLinkApi } from '@/service/systemApi'
import { goodsRecommendListApi, GoodsRecommendListRes } from '@/service/shoppingMallApi'
import { orderStore } from '@/store/orderStore'
import { BarType, ServerType } from '@/enums'
import CsRoundButton from '@/components/customer/csRoundButton.vue'
import { useToPath } from '@/hooks/useToPath'
import { articleListApi, ArticleListData, ArticleListRes } from '@/service/tutorialsApi'

defineOptions({
  name: 'Home',
})

const userStore = useUserStore()
const { userId, dataCode, inBlacklist } = storeToRefs(userStore)
const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const { serverType } = storeToRefs(useOrderStore)
const { toPath } = useToPath()

// 将 safeAreaInsets 改为响应式数据
const safeAreaInsets = ref(uni.getSystemInfoSync().safeAreaInsets || { top: 40 })

const opacity = ref(0)
const fistCompanyCode = ref('')
const recommendList = ref<GoodsRecommendListRes['data']>([])
const articleListFistItem = ref<ArticleListData>()
const articleList = ref<ArticleListData[]>([])
const carouses = [
  {
    // TODO id?
    url: '/pages/goodsDetail/index?goodsId=' + 24,
    img: PublicImgPath + 'images/index/mainImg_01.png',
  },
]
const carousesImgs = carouses.map((d) => d.img)

onLoad((option) => {
  console.log('onLoad:', option)
  // 更新 safeAreaInsets
  const sysInfo = uni.getSystemInfoSync()
  if (sysInfo.safeAreaInsets) {
    safeAreaInsets.value = sysInfo.safeAreaInsets
  }

  // 这个是短信转跳
  if ('smsop' in option) {
    // onLoad:{ smsop: "XGxvB"}
    fistCompanyCode.value = option.smsop
  }

  // getMallListData()
  getArticleList()
  userStore
    .login(fistCompanyCode.value)
    .then(() => {
      // 判断option为空
      // 这里有短信转跳、跟扫码登录pc
      // 扫码登录：option:{scene:"wxlogin%3Dxxxxxxxxxxxxxxxx"}
      if (Object.keys(option).length !== 0) {
        const query = JSON.stringify(option)
        console.log('query:', query)
        wxmalinkSignUrlLinkApi({ userInfoId: userId.value, query })
      }
      if (inBlacklist.value) {
        articleListFistItem.value = null
        articleList.value = []
        recommendList.value = []
        uni.showLoading({ title: '加载中...', mask: true })
      }
    })
    .catch((err) => {
      console.log('登录失败', err)
    })
})

const getArticleList = () => {
  articleListApi({ articleTypeId: 10 }).then((res) => {
    console.log('res', res)
    articleList.value = res.data[0].articleList
    articleListFistItem.value = articleList.value[0]
    articleList.value = articleList.value.slice(1)
  })
}

const getPath = (): string => {
  if (fistCompanyCode.value) {
    return `/pages/index/index?smsop=${fistCompanyCode.value}`
  } else if (dataCode.value) {
    return `/pages/index/index?smsop=${dataCode.value}`
  }
  return '/pages/index/index'
}

onShareAppMessage(() => {
  return {
    title: '迅码智联-专做标签打印机',
    path: getPath(),
  }
})
onShareTimeline(() => {
  // 朋友圈传参，参数因为单页模式限制拿不回来
  return {
    title: '迅码智联-专做标签打印机',
    path: getPath(),
  }
})

// 绑定分享参数
wx.onCopyUrl(() => {
  if (fistCompanyCode.value) {
    return { query: `smsop=${fistCompanyCode.value}` }
  } else if (dataCode.value) {
    return { query: `smsop=${dataCode.value}` }
  }
  return {}
})

onPageScroll((e) => {
  // scrollTop 最小为 0，最大为 40 时 opacity 达到 1
  const top = Math.max(0, Math.min(e.scrollTop, 40))
  opacity.value = top / 40
})

const handleSwiperClick = (index: number) => {
  if (inBlacklist.value) return
  const url = carouses[index].url
  uni.navigateTo({
    url,
  })
}

const getMallListData = () => {
  goodsRecommendListApi({}).then((res) => {
    recommendList.value = res.data
  })
}

const toGoodsDetail = (goodsId: number) => {
  if (goodsId !== 0) {
    uni.navigateTo({
      url: `/pages/goodsDetail/index?goodsId=${goodsId}`,
    })
  }
}

// 添加 onReady 生命周期，确保在页面渲染完成后再次获取
onReady(() => {
  // 页面渲染完成后再次获取，确保数据准确
  const sysInfo = uni.getSystemInfoSync()
  if (sysInfo.safeAreaInsets) {
    safeAreaInsets.value = sysInfo.safeAreaInsets
  }
})

// 跳转到文章详情页
const toArticleDetail = (articleId: number) => {
  uni.navigateTo({
    url: `/pages/tutorials/contextPage?id=${articleId}`,
  })
}
</script>
<template>
  <view :style="{ opacity: opacity }" style="position: relative; z-index: 9999; height: 0">
    <up-navbar fixed placeholder safeAreaInsetTop bgColor="#FF5B00">
      <template #left>
        <view class="flex items-center gap-1">
          <image
            :src="PublicImgPath + 'logo-white.svg'"
            alt="迅码智联"
            mode="heightFix"
            style="height: 10vw"
          />
          <view class="f-tag-card h-fit text-white font-bold px-2 py-0.5 rounded">智联</view>
        </view>
      </template>
    </up-navbar>
  </view>
  <view class="f-bg overflow-hidden py-4">
    <view
      class="flex items-center gap-2 px-4"
      :style="{ marginTop: (safeAreaInsets?.top || 40) + 'px' }"
    >
      <image
        :src="PublicImgPath + 'logo-white.svg'"
        alt="迅码智联"
        mode="heightFix"
        style="height: 13.4vw"
      />
      <view style="margin-top: 2.8vw" class="f-tag-card text-white font-bold px-2 py-0.5 rounded">
        智联
      </view>
    </view>
    <view class="text-white px-4" style="font-size: 5.3vw">
      <text style="font-weight: 100">只做</text>
      <text style="font-weight: 700">专业标签打印机</text>
    </view>
    <view class="f-main-box-1 w-full relative mt-2">
      <view class="f-bg-box-white o-bg-no absolute top-0 right-0"></view>
      <view class="f-bg-box-primary bg-primary absolute bottom-0 right-2"></view>
      <!-- <view class="f-main-img-1 absolute">
        <swiper class="f-swiper rounded-lg overflow-hidden" circular :autoplay="true" :interval="3000">
          <swiper-item
            v-for="(item, index) in carousesImgs"
            :key="index"
            @click="handleSwiperClick(index)"
          >
            <image :src="item" class="f-swiper w-full" />
          </swiper-item>
        </swiper>
      </view> -->
      <image
        class="f-main-img-1 absolute"
        :src="carousesImgs[0]"
        mode="widthFix"
        @click="handleSwiperClick(0)"
      />
    </view>
    <view class="px-4 mt-2 relative">
      <view
        class="f-main-box-2 flex flex-col"
        @click="toArticleDetail(articleListFistItem?.articleId)"
      >
        <view class="f-bg-box-primary bg-primary absolute bottom--2 left-2 z-1"></view>
        <image class="relative z-10" :src="articleListFistItem?.imageUrl" mode="widthFix" />
        <view class="px-4 py-2 relative bg-white z-10">
          {{ articleListFistItem?.articleTitle }}
        </view>
      </view>
    </view>

    <view class="px-2 mt-6">
      <template v-if="articleList.length === 0">
        <!-- 无数据时的占位 -->
      </template>
      <template v-else-if="articleList.length === 1">
        <!-- 单个元素时占满宽度 -->
        <view
          class="f-item bg-white flex flex-col mb-4 w-full"
          @click="toArticleDetail(articleList[0].articleId)"
        >
          <image class="w-full" :src="articleList[0].imageUrl" mode="widthFix" />
          <view class="text-sm px-2 py-2">
            {{ articleList[0]?.articleTitle }}
          </view>
        </view>
      </template>
      <template v-else>
        <!-- 多个元素时使用瀑布流布局 -->
        <view class="waterfall-container">
          <view class="waterfall-column">
            <view
              v-for="item in articleList.filter((_, i) => i % 2 === 0)"
              :key="item.articleId"
              class="f-item bg-white flex flex-col mb-2"
              @click="toArticleDetail(item.articleId)"
            >
              <image class="w-full" :src="item.imageUrl" mode="widthFix" />
              <view class="px-2 py-2">
                {{ item.articleTitle }}
              </view>
            </view>
          </view>
          <view class="waterfall-column">
            <view
              v-for="item in articleList.filter((_, i) => i % 2 === 1)"
              :key="item.articleId"
              class="f-item bg-white flex flex-col mb-2"
              @click="toArticleDetail(item.articleId)"
            >
              <image class="w-full" :src="item.imageUrl" mode="widthFix" />
              <view class="px-2 py-2">
                {{ item.articleTitle }}
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!--  <view class="flex flex-col space-y-3 mt-3">
      <view v-for="item in mainSale" :key="item.goodsId" @click="toGoodsDetail(item.goodsId)">
        <image :src="item.imageUrl" style="width: 100%" mode="widthFix" />
      </view>
    </view> -->
    <view class="py-4"></view>
  </view>

  <!-- <cs-round-button v-if="!inBlacklist" /> -->
</template>

<style lang="scss" scoped>
.f-bg {
  background: linear-gradient(90deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
  background-repeat: no-repeat;
  background-size: 100% 56vw;
}

.f-tag-card {
  background: linear-gradient(94deg, #181818 49.56%, #555 80.48%, #181818 98.01%);
}

$h: 323rpx;

.f-main-box-1 {
  height: $h;
}

.f-main-box-2 {
  box-shadow:
    0px 20rpx 24rpx -20rpx rgba(0, 0, 0, 0.1),
    0px 6rpx 30rpx -20rpx rgba(0, 0, 0, 0.5);
}

.f-main-img-1 {
  // height: 294rpx;
  top: 16rpx;
  left: 32rpx;
  width: calc(100vw - 64rpx);
  box-shadow: 0rpx 30rpx 24rpx -30rpx #000;
}

.f-main-img-2 {
  width: calc(100vw - 64rpx);
}

.f-bg-box-white {
  width: 330rpx;
  height: $h;
}

.f-bg-box-primary {
  $w: 290rpx;
  width: $w;
  height: $w;
}

.f-item {
  box-shadow: 0px 8rpx 30rpx -20rpx rgba(0, 0, 0, 0.25);
}

.waterfall-container {
  display: flex;
  width: 100%;
  gap: 12rpx;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
