<route lang="json5">
{ style: { navigationBarTitleText: '产品编码信息通报' } }
</route>

<script lang="ts" setup>
import ReportStep from '@/components/infoReport/reportStep.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'

const { loading, isShowStep, serveData, selectServe, handleSubmit } = useCreateOrder({
  orderConfirmUrl: '/pages/orderConfirm/infoReport',
})
</script>
<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <report-step v-if="isShowStep" />
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}通报步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="text-xs text-center o-color-aid mt-4">
      <text class="o-color-danger pr-2">*</text>
      更多数量请分多次订单
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
