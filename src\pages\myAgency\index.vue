<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的业务',
  },
}
</route>
<script lang="ts" setup>
import { PROJECT_3 } from '@/components/descriptionStr'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { useToPath } from '@/hooks/useToPath'
import RenewalStep from '@/components/agencyService/renewalStep.vue'
import {
  orderOtherInfoListApi,
  OrderOtherInfoListRes,
  OrderOtherInfoListResData,
} from '@/service/agencyServiceApi'
import { OrderStatus, ServerType } from '@/enums'
import RenewalTip from '@/components/agencyService/renewalTip.vue'
import { submitServiceStore } from '@/store/submitServiceStore'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import ModifyStep from '@/components/agencyService/modifyStep.vue'
import ModifyTip from '@/components/agencyService/modifyTip.vue'

const userStore = useUserStore()
const { registerServiceAuth, renewalServiceAuth, modifyServiceAuth, userId } =
  storeToRefs(userStore)
const useSubmitServiceStore = submitServiceStore()
const { orderInfoData } = storeToRefs(useSubmitServiceStore)

const { toPath } = useToPath()

const title = ref('')
const serviceAuth = ref(false)
const skipAuth = ref(false)
const isUnfold = ref(false)
const loading = ref(false)
const isShowEmptyBtn = ref(false)
const dataList = ref<OrderOtherInfoListResData[]>([])
const serviceData = ref()
const whichService = ref<ServerType>()

const getDataList = () =>
  new Promise<OrderOtherInfoListRes>((resolve, reject) => {
    dataList.value = []
    orderOtherInfoListApi({
      serverType: whichService.value,
      userId: userId.value,
    })
      .then((res) => {
        // 倒序
        dataList.value = [...res.data].reverse()
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        loading.value = false
      })
  })

const getColor = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.failed:
      return 'color-red'
    case OrderStatus.waitingSubmit:
      return 'color-yellow'
    case OrderStatus.done:
      return 'text-gray'
    default:
      return 'color-primary'
  }
}

const toService = (data: OrderOtherInfoListResData) => {
  orderInfoData.value = data
  let url = ''
  if (whichService.value) {
    switch (whichService.value) {
      case ServerType.renewalService:
        url = '/pages/myAgency/submitRenewal'
        break
      case ServerType.registerService:
        url = '/pages/myAgency/submitRegister'
        break
      case ServerType.modifyService:
        url = '/pages/myAgency/submitModify'
        break
    }
    uni.navigateTo({
      url,
    })
  }
}

const handleRefresh = () => {
  loading.value = true
  getDataList()
}

onLoad((option: any) => {
  // 跳过授权，用于成交后马上跳转
  skipAuth.value = option.skipAuth === '1'
  whichService.value = Number(option?.serverType) as ServerType
  if (whichService.value) {
    switch (whichService.value) {
      case ServerType.renewalService:
        title.value = '续展'
        serviceAuth.value = renewalServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          (item) => item.descriptionServiceType === 'renewalService',
        )[0]
        break
      case ServerType.registerService:
        title.value = '注册'
        serviceAuth.value = registerServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          (item) => item.descriptionServiceType === 'registerService',
        )[0]
        break
      case ServerType.modifyService:
        title.value = '变更'
        serviceAuth.value = modifyServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          (item) => item.descriptionServiceType === 'modifyService',
        )[0]
        break
    }
    uni.setNavigationBarTitle({
      title: '我的' + title.value,
    })
  }
})

onShow(() => {
  if (skipAuth.value) {
    loading.value = true
    setTimeout(() => {
      getDataList().then((res) => {
        isShowEmptyBtn.value = res.data.length === 0
      })
    }, 3000)
  } else if (serviceAuth.value) {
    loading.value = true
    getDataList()
  }
})
</script>

<template>
  <view v-if="skipAuth || serviceAuth">
    <view class="bg-white p-6">
      <RegisterStep v-if="whichService === ServerType.registerService" />
      <view
        class="relative overflow-hidden"
        :style="isUnfold ? 'height: auto' : 'height: 80vw'"
        v-if="whichService === ServerType.renewalService"
      >
        <RenewalStep />
        <view
          v-if="!isUnfold"
          @click="isUnfold = true"
          class="f-list-hidden w-full absolute bottom-0 left-0 z-1"
        >
          <view class="absolute bottom-0 left w-full text-xs text-center">展开</view>
        </view>
      </view>
      <ModifyStep v-if="whichService === ServerType.modifyService" />
    </view>
    <cs-long-button />
    <view class="px-4">
      <view class="font-bold mt-4">{{ title }}服务情况：</view>
      <view class="bg-white rounded-lg mt-2 p-4" v-for="item in dataList" :key="item.orderId">
        <view class="flex">
          <view class="grow-1">
            <view class="text-xs text-gray">支付时间：{{ item.payDate }}</view>
            <view class="text-xs text-gray mb-2">订单编号：{{ item.orderCode }}</view>
            <view>{{ item.orderContent }}</view>
          </view>
          <view class="shrink-0" :class="getColor(item.otherStatus)">
            {{ item.otherStatusStr }}
          </view>
        </view>
        <view v-if="item.otherStatus === OrderStatus.failed" class="color-red text-sm">
          {{ item.reason }}
        </view>
        <view
          class="o-bg-primary mt-3 py-1 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
          @click="toService(item)"
        >
          前往办理
        </view>
      </view>
      <up-loadmore status="loading" v-if="loading" />
      <view
        v-if="isShowEmptyBtn"
        class="mx-auto mt-4 px-8 py-2 o-border rounded-lg w-fit bg-white"
        @click="handleRefresh"
      >
        点击刷新
      </view>
      <view class="py-10"></view>
    </view>
  </view>
  <view v-else class="bg-white p-6">
    <view class="text-base font-bold mb-3">您未申请{{ title }}服务</view>
    <RenewalTip class="text-sm" v-if="whichService === ServerType.renewalService" />
    <ModifyTip v-if="whichService === ServerType.modifyService" />
    <view
      class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg o-bg-primary"
      @click="toPath(serviceData, 'navigateTo')"
    >
      前往办理{{ title }}业务
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-list-hidden {
  height: 26vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}
</style>
