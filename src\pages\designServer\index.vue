<route lang="json5">
{
  style: {
    navigationBarTitleText: '包装设计',
  },
}
</route>

<script lang="ts" setup>
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { DesignImage, PublicImgPath } from '@/components/image'
import ServerSwiper from '@/components/serverSwiper.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'

const {
  loading,
  isShowStep,
  customPrice,
  serveData,
  selectServe,
  descriptionServiceType,
  handleSubmit,
} = useCreateOrder({
  orderConfirmUrl: '/pages/orderConfirm/default',
  paymentSuccessUrl: '/pages/paymentSuccess/designServer',
  isMustCustomPrice: true,
  noPriceCallBack: () => {
    uni.pageScrollTo({ selector: '#price' })
  },
})
const isShowDemo = ref(false)

const downloadDescription = [
  '选择一张喜欢的模板。',
  '付款后，联系客服获取想要的那张模板。',
  '获取的模板是可编辑的源文件。',
  '可自行将模板中的文字、logo替换成合适的内容。',
]

const labelDemo = [
  PublicImgPath + 'labelDesign/label_001.jpg',
  PublicImgPath + 'labelDesign/label_002.jpg',
  PublicImgPath + 'labelDesign/label_003.jpg',
]

const previewImg = (index: number) => {
  uni.previewImage({
    urls: labelDemo,
    current: index,
  })
}
</script>
<template>
  <server-swiper :carouses-list="DesignImage.designServer.carouses" />
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <view class="font-bold text-sm mb-2">印前合规检查步骤：</view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].makeStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index font-bold o-color-primary flex-shrink-0">{{ index + 1 }}.</view>
          <view>{{ item }}</view>
        </view>
        <view class="font-bold text-sm mt-6 mb-2">设计服务步骤：</view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].designStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index font-bold o-color-primary flex-shrink-0">{{ index + 1 }}.</view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <view class="bg-white py-4 rounded-lg mt-3">
      <view class="px-4 pt-2 mb-6">
        <view class="font-bold text-sm mb-2">自助模板下载：</view>
        <view v-for="(item, index) in downloadDescription" :key="index" class="flex text-xs">
          <view class="f-index font-bold o-color-primary flex-shrink-0">{{ index + 1 }}.</view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="overflow-hidden relative" :class="isShowDemo ? '' : 'h-50vw'">
        <up-image
          v-for="(item, index) in labelDemo"
          width="100%"
          mode="widthFix"
          :key="index"
          :src="item"
          @click="previewImg(index)"
        />
        <view v-show="!isShowDemo" class="f-list-hidden w-full absolute bottom-0 left-0 z-1"></view>
      </view>
      <view
        class="w-full flex items-center justify-center gap-3 text-xs o-color-aid"
        @click="isShowDemo = !isShowDemo"
      >
        <up-icon name="question-circle" :size="18"></up-icon>
        <view>{{ isShowDemo ? '隐藏' : '显示' }}自助模板</view>
        <up-icon v-if="isShowDemo" name="arrow-up" :size="16"></up-icon>
        <up-icon v-else name="arrow-down" :size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rounded-lg">
      <view class="font-bold pt-2 mb-3">选择服务：</view>
      <view class="mt-4" id="targetElement">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
      <view class="mt-5 mb-2 text-sm font-bold">请先联系客服，沟通好价格后，填写金额：</view>
      <view class="flex">
        <view id="price" class="f-label text-sm mt-2 shrink-0">
          <text class="pr-1 o-color-danger">*</text>
          协定金额：
        </view>
        <up-input border="bottom" v-model="customPrice" :maxlength="10" type="number" clearable />
        <view class="text-sm mt-2">元</view>
      </view>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-label {
  flex-shrink: 0;
  width: 26vw;
}

.f-index {
  width: 1.2rem;
}

.f-w {
  bottom: -0.8rem;
  left: 100rpx;
}

.f-h {
  top: 70rpx;
  left: -2.5rem;
  transform: rotate(-90deg);
}

:deep(.f-table) {
  // border-bottom: 1px solid #e4e4e4;
  &:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

.f-serve {
  width: 540rpx;
}

.f-list-hidden {
  height: 13vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}
</style>
