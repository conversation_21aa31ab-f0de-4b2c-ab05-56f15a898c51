<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { useServiceStore } from '@/store/serviceStore'
import { storeToRefs } from 'pinia'
import {
  payReportOrderApi,
  sumbitReportOrderApi,
  SumbitReportOrderParams,
} from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { wxRequestPayment } from '@/components/mix'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { useUserStore } from '@/store/user'
import { Article } from '@/enums'
import { addressPageApi } from '@/service/addressPageApi'
import { catchErrorAndNavigateBack } from '@/utils'
import { replaceComma } from '@/utils/tool'
import { msgModalStore } from '@/store/msgModalStore'
import debounce from 'debounce'
const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const {
  couponId,
  orderCode,
  tempType,
  price,
  total,
  tempName,
  couponPrice,
  discount,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  couponType,
  isSelectedCoupon,
  vendorCode,
} = storeToRefs(useOrderStore)

const btnDisable = ref(false)
const btnStr = ref('提交订单')
const agree = ref(false)
const isModalShow = ref(false)

const addressData = reactive({
  addressDetail: '',
  district: '',
  phone: '',
  realName: '',
})

onLoad(() => {
  // console.log(option)
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()
  getOrderPrice()
})

onShow(() => {
  getAddressData()
})

const getOrderPrice = (withAddress: boolean = false) =>
  new Promise((resolve, reject) => {
    btnDisable.value = true
    const params: SumbitReportOrderParams = {
      orderCode: orderCode.value,
    }
    if (withAddress) {
      params.addressDetail = addressData.addressDetail
      params.contact = addressData.realName
      params.contactPhone = addressData.phone
      params.district = addressData.district
    }
    sumbitReportOrderApi(params)
      .then((res: any) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        price.value = d.price
        total.value = d.total
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice
        tempName.value = d.tempName
        // d.tempType 后期不用了，改用serverType
        tempType.value = d.serverType
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })

// TODO 未做抖音
const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (addressData.phone) {
        if (agree.value) {
          getOrderPrice(true).then(() => {
            payReportOrderApi({
              orderCode: orderCode.value,
            })
              .then((resData: any) => {
                const url = '/pages/paymentSuccess/default'
                if (resData.data.isNeedToPay) {
                  wxRequestPayment(resData.data)
                    .then(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                    })
                } else {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch((err: string) => {
                btnDisable.value = false
                uni.hideLoading()
                catchErrorAndNavigateBack(err)
              })
          })
        } else {
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: '请先勾选已阅读并同意《付款免责声明》',
            })
            .then(() => {
              uni.pageScrollTo({
                selector: '#agreeElement',
              })
            })
        }
      } else {
        useMsgModalStore.confirm({
          title: '温馨提示',
          content: '请先添加地址',
        })
      }
    }
  },
  1000,
  { immediate: true },
)

const toDiscountCouponPage = () => {
  uni.navigateTo({
    url: '/pages/discountCoupon/index?toSelect=true',
  })
}

const getAddressData = () => {
  addressPageApi({
    // isDefault: false,
    // groupBy: "",
    // needTotalCount: true,
    // orderBy: 'createdDate',
    // orderDirection: OrderDirection.desc,
    // pageIndex: 1,
    // pageSize: 100000,
    userId: userId.value,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      const d = res.data.filter((item) => item.isDefault)
      if (d.length > 0) {
        addressData.addressDetail = d[0].addressDetail
        addressData.district = d[0].district
        addressData.phone = d[0].phone
        addressData.realName = d[0].realName
      } else {
        addressData.addressDetail = res.data[0].addressDetail
        addressData.district = res.data[0].district
        addressData.phone = res.data[0].phone
        addressData.realName = res.data[0].realName
      }
    }
  })
}

const handleAddressClick = () => {
  uni.navigateTo({
    url: '/pages/addressPage/index?toSelect=true',
  })
}

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :articleIds="[Article.labelPrint, Article.platform]" />
  </up-modal>
  <view class="bg-white p-4 flex justify-between items-end">
    <view class="pl-2">
      <view class="o-color-primary text-xl font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="text-sm o-color-aid">{{ DESCRIPTION_STR[descriptionServiceType].typeStr }}</view>
      <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg mt-3">
      <view class="font-bold mt-2 mb-3">收货地址：</view>
      <view class="flex gap-4 justify-between" @click="handleAddressClick">
        <view v-if="addressData.phone">
          <view class="flex gap-4">
            <text>{{ addressData.realName }}</text>
            <text>{{ addressData.phone }}</text>
          </view>
          <view class="text-sm">{{ addressData.district }}</view>
          <view class="text-sm">{{ addressData.addressDetail }}</view>
        </view>
        <view v-else class="text-gray">请添加地址</view>
        <view class="shrink-0 o-color-aid pr-1">
          <up-icon name="arrow-right" :size="14"></up-icon>
        </view>
      </view>
    </view>
    <view class="bg-white p-4 rounded-lg mt-3">
      <view class="font-bold mt-2 mb-3">所选方案：</view>
      <view class="flex gap-4 justify-between">
        <view>{{ replaceComma(tempName) }}</view>
        <view class="shrink-0">
          <text class="font-bold">{{ price }}</text>
          元
        </view>
      </view>
      <view class="o-line mt-3 mb-3"></view>
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box :price="actuallyPrice" :size="48" class="o-color-danger" />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="flex justify-center items-center mt-6 text-xs">
      <up-checkbox
        usedAlone
        v-model:checked="agree"
        labelSize="12"
        :size="14"
        label="我已阅读并同意"
      ></up-checkbox>
      <view class="o-color-primary" @click.stop="isModalShow = true">《付款免责声明》</view>
    </view>
    <view class="p-11"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary'"
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
