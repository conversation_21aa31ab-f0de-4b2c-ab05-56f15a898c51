import { CustomRequestOptions } from '@/interceptors/request'
import { DefaultDataRes } from '@/interceptors/myInterceptors'

export const http = <T extends DefaultDataRes = DefaultDataRes>(options: CustomRequestOptions) => {
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const r = res.data as T
          if (r.success) {
            // 2.1 提取核心数据 res.data
            resolve(r)
          } else {
            if (r.errMessage === 'Token过期') {
              uni.switchTab({
                url: '/pages/userPage/index',
              })
            } else {
              if (!options.hideErrorToast) {
                uni.showToast({
                  icon: 'none',
                  title: r.errMessage || '请求错误',
                })
              }
            }
            reject(r.errMessage)
          }
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          if (!options.hideErrorToast) {
            uni.showToast({
              icon: 'none',
              title: '请求错误',
            })
          }
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export const httpGet = <T extends DefaultDataRes>(url: string, query?: Record<string, any>) => {
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param hideErrorToast 是否隐藏报错提示
 * @returns
 */
export const httpPost = <T extends DefaultDataRes>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  hideErrorToast?: boolean,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    hideErrorToast,
  })
}

http.get = httpGet
http.post = httpPost
