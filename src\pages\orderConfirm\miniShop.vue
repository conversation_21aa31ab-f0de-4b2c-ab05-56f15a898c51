<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { storeToRefs } from 'pinia'
import { getDouYinOrderPreDataApi, payQrOrderApi, sumbitQrOrderApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { douYinRequestPayment, wxRequestPayment } from '@/components/mix'
import { Article } from '@/enums'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { miniShopOrderStore } from '@/store/orderStore/miniShopOrderStore'
import { useServiceStore } from '@/store/serviceStore'
import { catchErrorAndNavigateBack, getMiniShopTempTypeStr } from '@/utils'
import { msgModalStore } from '@/store/msgModalStore'
import debounce from 'debounce'
const useMsgModalStore = msgModalStore()

const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useMiniShopOrderStore = miniShopOrderStore()
const { orderCode, tempName, tempType, capacity, flow } = storeToRefs(useMiniShopOrderStore)
const useOrderStore = orderStore()
const {
  couponId,
  couponType,
  discount,
  couponPrice,
  // orderCode,
  // tempType,
  price,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  isSelectedCoupon,
} = storeToRefs(useOrderStore)

const btnDisable = ref(false)
const btnStr = ref('提交订单')
const agree = ref(false)
const isModalShow = ref(false)

onLoad(() => {
  // console.log(option)
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()
  getOrderPrice()
})

/**
 * 选优惠券计算优惠
 */
const getOrderPrice = () =>
  new Promise((resolve, reject) => {
    btnDisable.value = true
    sumbitQrOrderApi({
      orderCode: orderCode.value,
      // couponId: couponId.value,  // 先不接入优惠券
    })
      .then((res) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        price.value = d.price
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })

const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (agree.value) {
        const url = '/pages/paymentSuccess/miniShop'
        btnDisable.value = true
        uni.showLoading({
          title: '支付中',
        })

        // #ifdef MP-WEIXIN
        // 微信支付逻辑
        payQrOrderApi({
          orderCode: orderCode.value,
        })
          .then((resData: any) => {
            if (resData.data.isNeedToPay) {
              wxRequestPayment(resData.data)
                .then(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                })
                .catch(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                })
            } else {
              btnDisable.value = false
              uni.hideLoading()
              uni.redirectTo({
                url,
              })
            }
          })
          .catch((err: string) => {
            btnDisable.value = false
            uni.hideLoading()
            catchErrorAndNavigateBack(err)
          })
        // #endif

        // #ifdef MP-TOUTIAO
        // 抖音支付逻辑
        getDouYinOrderPreDataApi({
          orderCode: orderCode.value,
          params: '',
          path: 'pages/index/index',
        }).then((res) => {
          // console.log(res.data)
          if (res.data.isZeroOrder) {
            // 无需支付
            btnDisable.value = false
            uni.hideLoading()
            uni.redirectTo({
              url,
            })
          } else {
            const d = {
              orderCode: orderCode.value,
              ...res.data,
            }
            douYinRequestPayment(d)
              .then(() => {
                btnDisable.value = false
                uni.hideLoading()
                uni.redirectTo({
                  url,
                })
              })
              .catch(() => {
                btnDisable.value = false
                uni.hideLoading()
              })
          }
        })
        // #endif
      } else {
        useMsgModalStore
          .confirm({
            title: '温馨提示',
            content: '请先勾选已阅读并同意《付款免责声明》《隐私政策》',
          })
          .then(() => {
            uni.pageScrollTo({
              selector: '#agreeElement',
            })
          })
      }
    }
  },
  1000,
  { immediate: true },
)

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :articleIds="[Article.miniShop, Article.privacy, Article.platform]" />
  </up-modal>
  <view class="bg-white p-4 flex justify-between items-end">
    <view class="pl-2">
      <view class="o-color-primary text-xl font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="text-sm o-color-aid">{{ DESCRIPTION_STR[descriptionServiceType].typeStr }}</view>
      <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rounded-lg mt-3">
      <view class="font-bold mt-2 mb-3">所选套餐方案：</view>
      <view class="flex gap-4 justify-between">
        <view>{{ tempName }}</view>
        <view class="flex items-baseline shrink-0">
          <view class="font-bold">{{ price }}</view>
          <view class="text-gray text-xs">/{{ getMiniShopTempTypeStr(tempType) }}</view>
        </view>
      </view>
      <view class="o-line mt-3 mb-3"></view>
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box :price="actuallyPrice" :size="48" class="o-color-danger" />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="flex justify-center items-center mt-6 text-xs">
      <up-checkbox
        usedAlone
        v-model:checked="agree"
        labelSize="12"
        :size="14"
        label="我已阅读并同意"
      ></up-checkbox>
      <view class="o-color-primary" @click.stop="isModalShow = true">《付款免责声明》</view>
    </view>
    <view class="p-11"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary'"
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded-lg"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
